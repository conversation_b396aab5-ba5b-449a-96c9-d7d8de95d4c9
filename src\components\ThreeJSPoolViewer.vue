<template>
  <div class="relative h-full w-full flex-1" ref="container">
    <!-- 养鱼池信息弹窗 -->
    <div
      v-if="showPoolInfo"
      class="pool-info-popup"
      :class="{
        'alarm-popup': selectedPoolInfo.isAlarm,
        'popup-below': popupPosition.isBelow,
      }"
      :style="{
        left: popupPosition.x + 'px',
        top: popupPosition.y + 'px',
        '--arrow-left': popupPosition.arrowLeft + 'px',
      }"
    >
      <div class="popup-header">
        <h3 class="popup-title">
          {{ selectedPoolInfo.name }}
          <span v-if="selectedPoolInfo.isAlarm" class="alarm-badge">报警</span>
        </h3>
        <button class="close-btn" @click="closePopup">×</button>
      </div>
      <div class="popup-content">
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">PH值</div>
            <div class="info-value">{{ selectedPoolInfo.ph }}</div>
          </div>
          <div class="info-item">
            <div class="info-label">水温</div>
            <div class="info-value">{{ selectedPoolInfo.temperature }}°C</div>
          </div>
          <div class="info-item">
            <div class="info-label">溶解氧</div>
            <div class="info-value">{{ selectedPoolInfo.oxygen }} mg/L</div>
          </div>
          <div class="info-item">
            <div class="info-label">水位</div>
            <div class="info-value">{{ selectedPoolInfo.waterLevel }} m</div>
          </div>
          <div class="info-item">
            <div class="info-label">盐度</div>
            <div class="info-value">{{ selectedPoolInfo.salinity }}%</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import * as THREE from "three";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js";

// 导入lil.gui
import { GUI } from "three/examples/jsm/libs/lil-gui.module.min.js";

// 获取容器引用
const container = ref<HTMLDivElement>();

// 弹窗相关响应式数据
const showPoolInfo = ref(false);
const popupPosition = ref({ x: 0, y: 0, arrowLeft: 0, isBelow: false });
const selectedPoolInfo = ref({
  name: "",
  ph: "",
  temperature: "",
  oxygen: "",
  waterLevel: "",
  salinity: "",
  isAlarm: false,
});

// 轮播相关变量
let autoRotationTimer: number | null = null;
let currentRotationIndex = 0;
const isAutoRotating = ref(false);

// 养鱼池数据
let poolsData: any[] = [];

// 正常范围定义
const normalRanges = {
  water_temperature: { min: 20, max: 29 },
  PH: { min: 7.3, max: 8.8 },
  salinity: { min: 10, max: 26 },
  water_level: { min: 0.7, max: 1.45 },
  dissolved_oxygen: { min: 5, max: Infinity },
};

// Three.js 相关变量
let scene: THREE.Scene;
let camera: THREE.PerspectiveCamera;
let renderer: THREE.WebGLRenderer;

let poolModel: THREE.Group | null = null;
let animationId: number;
let raycaster: THREE.Raycaster;
let mouse: THREE.Vector2;
let poolObjects: THREE.Object3D[] = [];
let selectedPool: THREE.Object3D | null = null;
let originalMaterials: Map<THREE.Object3D, THREE.Material | THREE.Material[]> =
  new Map();

// GUI 相关变量
let gui: GUI | null = null;
let ambientLight: THREE.AmbientLight;
let directionalLight: THREE.DirectionalLight;

// GUI 控制参数
const guiParams = {
  // 相机控制
  camera: {
    x: 0,
    y: 28.8,
    z: 25,
    fov: 45,
    lookAtX: 0,
    lookAtY: 9.5,
    lookAtZ: 0,
    resetCamera: () => resetCamera(),
  },
  // 光照控制
  lighting: {
    ambientIntensity: 1.2,
    ambientColor: "#ffffff",
    directionalIntensity: 1.5,
    directionalColor: "#ffffff",
    directionalX: 10,
    directionalY: 15,
    directionalZ: 5,
  },
  // 模型控制
  model: {
    scale: 0.57,
    x: 0,
    y: 0,
    z: 0,
    rotationX: 0,
    rotationY: 0,
    rotationZ: 0,
  },
  // 轮播控制
  rotation: {
    enabled: false,
    speed: 3000,
    start: () => startAutoRotation(),
    stop: () => stopAutoRotation(),
  },
  // 显示控制
  display: {
    showGrid: true,
    showAxes: true,
    wireframe: false,
  },
};

// 加载养鱼池数据
async function loadPoolsData() {
  try {
    const response = await fetch("/data.json");
    poolsData = await response.json();
    console.log("养鱼池数据加载成功:", poolsData.length, "个池子");
  } catch (error) {
    console.error("加载养鱼池数据失败:", error);
    // 如果加载失败，使用默认数据
    poolsData = [];
  }
}

// 重置相机位置
function resetCamera() {
  if (!camera) return;

  camera.position.set(
    guiParams.camera.x,
    guiParams.camera.y,
    guiParams.camera.z
  );
  camera.fov = guiParams.camera.fov;
  camera.updateProjectionMatrix();

  // 使用GUI中的lookAt参数
  camera.lookAt(
    guiParams.camera.lookAtX,
    guiParams.camera.lookAtY,
    guiParams.camera.lookAtZ
  );

  // 不需要重置控制器，因为没有创建
}

// 初始化GUI
function initGUI() {
  // 如果GUI已经存在，先销毁它
  if (gui) {
    gui.destroy();
  }

  // 创建GUI
  gui = new GUI();
  gui.title("养鱼池控制面板");

  // 相机控制文件夹
  const cameraFolder = gui.addFolder("相机控制");
  cameraFolder
    .add(guiParams.camera, "x", -20, 30, 0.1)
    .name("X位置")
    .onChange(() => updateCamera());
  cameraFolder
    .add(guiParams.camera, "y", 0, 30, 0.1)
    .name("Y位置")
    .onChange(() => updateCamera());
  cameraFolder
    .add(guiParams.camera, "z", -20, 30, 0.1)
    .name("Z位置")
    .onChange(() => updateCamera());
  cameraFolder
    .add(guiParams.camera, "fov", 10, 120, 1)
    .name("视角")
    .onChange(() => updateCamera());

  // LookAt控制
  cameraFolder
    .add(guiParams.camera, "lookAtX", -20, 20, 0.1)
    .name("看向X")
    .onChange(() => updateCamera());
  cameraFolder
    .add(guiParams.camera, "lookAtY", -20, 20, 0.1)
    .name("看向Y")
    .onChange(() => updateCamera());
  cameraFolder
    .add(guiParams.camera, "lookAtZ", -20, 20, 0.1)
    .name("看向Z")
    .onChange(() => updateCamera());

  cameraFolder.add(guiParams.camera, "resetCamera").name("重置相机");

  // 光照控制文件夹
  const lightingFolder = gui.addFolder("光照控制");
  lightingFolder
    .add(guiParams.lighting, "ambientIntensity", 0, 2, 0.1)
    .name("环境光强度")
    .onChange(() => updateLighting());
  lightingFolder
    .addColor(guiParams.lighting, "ambientColor")
    .name("环境光颜色")
    .onChange(() => updateLighting());
  lightingFolder
    .add(guiParams.lighting, "directionalIntensity", 0, 2, 0.1)
    .name("方向光强度")
    .onChange(() => updateLighting());
  lightingFolder
    .addColor(guiParams.lighting, "directionalColor")
    .name("方向光颜色")
    .onChange(() => updateLighting());
  lightingFolder
    .add(guiParams.lighting, "directionalX", -20, 20, 0.5)
    .name("方向光X")
    .onChange(() => updateLighting());
  lightingFolder
    .add(guiParams.lighting, "directionalY", -20, 20, 0.5)
    .name("方向光Y")
    .onChange(() => updateLighting());
  lightingFolder
    .add(guiParams.lighting, "directionalZ", -20, 20, 0.5)
    .name("方向光Z")
    .onChange(() => updateLighting());

  // 模型控制文件夹
  const modelFolder = gui.addFolder("模型控制");
  modelFolder
    .add(guiParams.model, "scale", 0.01, 1, 0.001)
    .name("缩放")
    .onChange(() => updateModel());
  modelFolder
    .add(guiParams.model, "x", -10, 10, 0.1)
    .name("X位置")
    .onChange(() => updateModel());
  modelFolder
    .add(guiParams.model, "y", -5, 5, 0.1)
    .name("Y位置")
    .onChange(() => updateModel());
  modelFolder
    .add(guiParams.model, "z", -10, 20, 0.1)
    .name("Z位置")
    .onChange(() => updateModel());
  modelFolder
    .add(guiParams.model, "rotationX", -Math.PI, Math.PI, 0.01)
    .name("X轴旋转")
    .onChange(() => updateModel());
  modelFolder
    .add(guiParams.model, "rotationY", -Math.PI, Math.PI, 0.01)
    .name("Y轴旋转")
    .onChange(() => updateModel());
  modelFolder
    .add(guiParams.model, "rotationZ", -Math.PI, Math.PI, 0.01)
    .name("Z轴旋转")
    .onChange(() => updateModel());

  // 轮播控制文件夹
  const rotationFolder = gui.addFolder("轮播控制");
  rotationFolder
    .add(guiParams.rotation, "enabled")
    .name("启用轮播")
    .listen()
    .onChange((value: boolean) => {
      if (value) {
        startAutoRotation();
      } else {
        stopAutoRotation();
      }
    });
  rotationFolder
    .add(guiParams.rotation, "speed", 1000, 10000, 100)
    .name("轮播速度(ms)");
  rotationFolder.add(guiParams.rotation, "start").name("开始轮播");
  rotationFolder.add(guiParams.rotation, "stop").name("停止轮播");

  // 显示控制文件夹
  const displayFolder = gui.addFolder("显示控制");
  displayFolder
    .add(guiParams.display, "showGrid")
    .name("显示网格")
    .onChange(() => updateDisplay());
  displayFolder
    .add(guiParams.display, "showAxes")
    .name("显示坐标轴")
    .onChange(() => updateDisplay());
  displayFolder
    .add(guiParams.display, "wireframe")
    .name("线框模式")
    .onChange(() => updateDisplay());

  // 调试控制
  const debugFolder = gui.addFolder("调试控制");
  let debugObj = {
    debugObjects: function () {
      if (poolModel) {
        debugModelObjects(poolModel);
      }
    },
  };
  debugFolder.add(debugObj, "debugObjects").name("调试对象名称");

  // 水面控制
  const waterFolder = gui.addFolder("水面控制");
  let waterParams = {
    opacity: 0.7,
    color: "#87ceeb",
    roughness: 0.05,
    transmission: 0.6,
    ior: 1.33,
    reflectivity: 0.8,
    reprocessWater: function () {
      if (poolModel) {
        processWaterMaterials(poolModel);
      }
    },
    forceWaterMaterial: function () {
      if (poolModel) {
        forceApplyWaterMaterial(poolModel);
      }
    },
  };

  waterFolder
    .add(waterParams, "opacity", 0, 1, 0.1)
    .name("水面透明度")
    .onChange(() => updateWaterMaterials(waterParams));
  waterFolder
    .addColor(waterParams, "color")
    .name("水面颜色")
    .onChange(() => updateWaterMaterials(waterParams));
  waterFolder
    .add(waterParams, "roughness", 0, 1, 0.1)
    .name("水面粗糙度")
    .onChange(() => updateWaterMaterials(waterParams));
  waterFolder
    .add(waterParams, "transmission", 0, 1, 0.1)
    .name("透射度")
    .onChange(() => updateWaterMaterials(waterParams));
  waterFolder
    .add(waterParams, "ior", 1, 2, 0.1)
    .name("折射率")
    .onChange(() => updateWaterMaterials(waterParams));
  waterFolder
    .add(waterParams, "reflectivity", 0, 1, 0.1)
    .name("反射率")
    .onChange(() => updateWaterMaterials(waterParams));
  waterFolder.add(waterParams, "reprocessWater").name("重新处理水面");
  waterFolder.add(waterParams, "forceWaterMaterial").name("强制应用水面材质");

  // 混凝土柱子控制
  const concreteFolder = gui.addFolder("混凝土柱子控制");
  let concreteParams = {
    color: "#a8a8a8",
    roughness: 0.85,
    metalness: 0.0,
    envMapIntensity: 0.3,
    reprocessConcrete: function () {
      if (poolModel) {
        processConcreteColumnMaterials(poolModel);
      }
    },
    updateConcrete: function () {
      if (poolModel) {
        updateConcreteColumnMaterials(poolModel, concreteParams);
      }
    },
  };

  concreteFolder
    .addColor(concreteParams, "color")
    .name("混凝土颜色")
    .onChange(() => concreteParams.updateConcrete());
  concreteFolder
    .add(concreteParams, "roughness", 0, 1, 0.1)
    .name("粗糙度")
    .onChange(() => concreteParams.updateConcrete());
  concreteFolder
    .add(concreteParams, "metalness", 0, 1, 0.1)
    .name("金属度")
    .onChange(() => concreteParams.updateConcrete());
  concreteFolder
    .add(concreteParams, "envMapIntensity", 0, 1, 0.1)
    .name("环境反射强度")
    .onChange(() => concreteParams.updateConcrete());
  concreteFolder
    .add(concreteParams, "reprocessConcrete")
    .name("重新随机处理柱子");
  concreteFolder.add(concreteParams, "updateConcrete").name("应用当前设置");

  // 全屏控制
  let eventObj = {
    Fullscreen: function () {
      document.body.requestFullscreen();
      console.log("全屏");
    },
    ExitFullscreen: function () {
      document.exitFullscreen();
      console.log("退出全屏");
    },
  };
  gui.add(eventObj, "Fullscreen").name("全屏");
  gui.add(eventObj, "ExitFullscreen").name("退出全屏");

  console.log("GUI初始化完成");
}

// 更新相机
function updateCamera() {
  if (!camera) return;

  camera.position.set(
    guiParams.camera.x,
    guiParams.camera.y,
    guiParams.camera.z
  );
  camera.fov = guiParams.camera.fov;
  camera.updateProjectionMatrix();

  // 使用GUI中的lookAt参数
  camera.lookAt(
    guiParams.camera.lookAtX,
    guiParams.camera.lookAtY,
    guiParams.camera.lookAtZ
  );
}

// 更新光照
function updateLighting() {
  if (ambientLight) {
    ambientLight.intensity = guiParams.lighting.ambientIntensity;
    ambientLight.color.setHex(
      parseInt(guiParams.lighting.ambientColor.replace("#", "0x"))
    );
  }

  if (directionalLight) {
    directionalLight.intensity = guiParams.lighting.directionalIntensity;
    directionalLight.color.setHex(
      parseInt(guiParams.lighting.directionalColor.replace("#", "0x"))
    );
    directionalLight.position.set(
      guiParams.lighting.directionalX,
      guiParams.lighting.directionalY,
      guiParams.lighting.directionalZ
    );
  }
}

// 更新模型
function updateModel() {
  if (!poolModel) return;

  poolModel.scale.setScalar(guiParams.model.scale);
  poolModel.position.set(
    guiParams.model.x,
    guiParams.model.y,
    guiParams.model.z
  );
  poolModel.rotation.x = guiParams.model.rotationX;
  poolModel.rotation.y = guiParams.model.rotationY;
  poolModel.rotation.z = guiParams.model.rotationZ;
}

// 更新显示
function updateDisplay() {
  // if (gridHelper) {
  //   gridHelper.visible = guiParams.display.showGrid;
  // }

  // if (axesHelper) {
  //   axesHelper.visible = guiParams.display.showAxes;
  // }

  if (poolModel) {
    poolModel.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        if (Array.isArray(child.material)) {
          child.material.forEach((material) => {
            material.wireframe = guiParams.display.wireframe;
          });
        } else {
          child.material.wireframe = guiParams.display.wireframe;
        }
      }
    });
  }
}

// 检查数值是否在正常范围内
function isValueNormal(key: string, value: number): boolean {
  const range = normalRanges[key as keyof typeof normalRanges];
  if (!range) return true;

  if (range.max === Infinity) {
    return value >= range.min;
  }
  return value >= range.min && value <= range.max;
}

// 检查池子是否有报警
function checkPoolAlarm(poolData: any): boolean {
  return (
    !isValueNormal("water_temperature", poolData.water_temperature) ||
    !isValueNormal("PH", poolData.PH) ||
    !isValueNormal("salinity", poolData.salinity) ||
    !isValueNormal("water_level", poolData.water_level) ||
    !isValueNormal("dissolved_oxygen", poolData.dissolved_oxygen)
  );
}

// 从池子对象中获取编号
function getPoolNumber(poolObject: THREE.Object3D): number {
  // 优先使用我们分配的编号
  if (poolObject.userData && poolObject.userData.poolNumber) {
    return poolObject.userData.poolNumber;
  }

  // 如果没有分配编号，尝试从名称中提取
  const match = poolObject.name.match(/\d+/);
  if (match) {
    return parseInt(match[0]);
  }

  // 如果都没有找到，返回1作为默认值
  return 1;
}

// 获取池子数据
function getPoolData(poolIndex: number) {
  // 确保索引在有效范围内，如果超出范围则取前36个
  const dataIndex = Math.min(poolIndex - 1, 35);
  const data = poolsData[dataIndex] || {
    id: poolIndex,
    water_temperature: 25,
    PH: 7.5,
    dissolved_oxygen: 6,
    water_level: 1.0,
    salinity: 15,
  };

  const isAlarm = checkPoolAlarm(data);

  return {
    name: `养鱼池${poolIndex.toString().padStart(2, "0")}`,
    ph: data.PH.toFixed(1),
    temperature: data.water_temperature.toFixed(1),
    oxygen: data.dissolved_oxygen.toFixed(1),
    waterLevel: data.water_level.toFixed(2),
    salinity: data.salinity.toString(),
    isAlarm,
  };
}

// 初始化 Three.js
function initThreeJS() {
  if (!container.value) return;

  // 获取容器尺寸
  const width = container.value.clientWidth;
  const height = container.value.clientHeight;

  // 创建场景
  scene = new THREE.Scene();

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    45, // 视角
    width / height, // 宽高比
    0.1, // 近平面
    1000 // 远平面
  );

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({
    antialias: true,
    alpha: true, // 启用透明度支持
    logarithmicDepthBuffer: true, // 改善深度缓冲精度
  });
  renderer.setSize(width, height);
  renderer.setPixelRatio(window.devicePixelRatio);

  // 设置透明背景
  renderer.setClearColor(0x000000, 0); // 黑色背景，透明度为0

  // 启用阴影
  renderer.shadowMap.enabled = true;
  renderer.shadowMap.type = THREE.PCFSoftShadowMap;

  // 设置色调映射以获得更好的视觉效果
  renderer.toneMapping = THREE.ACESFilmicToneMapping;
  renderer.toneMappingExposure = 1.0;

  // 将渲染器添加到容器中
  container.value.appendChild(renderer.domElement);

  // 初始化射线投射器和鼠标向量
  raycaster = new THREE.Raycaster();
  mouse = new THREE.Vector2();

  // 添加鼠标点击事件监听器
  renderer.domElement.addEventListener("click", onMouseClick, false);

  // 添加光源
  ambientLight = new THREE.AmbientLight(
    parseInt(guiParams.lighting.ambientColor.replace("#", "0x")),
    guiParams.lighting.ambientIntensity
  );
  scene.add(ambientLight);

  directionalLight = new THREE.DirectionalLight(
    parseInt(guiParams.lighting.directionalColor.replace("#", "0x")),
    guiParams.lighting.directionalIntensity
  );
  directionalLight.position.set(
    guiParams.lighting.directionalX,
    guiParams.lighting.directionalY,
    guiParams.lighting.directionalZ
  );
  directionalLight.castShadow = true;
  scene.add(directionalLight);

  // 添加额外的点光源来增强照明
  const pointLight1 = new THREE.PointLight(0xffffff, 0.8, 50);
  pointLight1.position.set(15, 20, 15);
  scene.add(pointLight1);

  const pointLight2 = new THREE.PointLight(0xffffff, 0.8, 50);
  pointLight2.position.set(-15, 20, -15);
  scene.add(pointLight2);

  // 添加半球光来模拟天空光照
  const hemisphereLight = new THREE.HemisphereLight(0x87ceeb, 0x444444, 0.6);
  scene.add(hemisphereLight);

  // 加载FBX模型
  loadPoolModel();

  // 设置相机位置
  camera.position.set(
    guiParams.camera.x,
    guiParams.camera.y,
    guiParams.camera.z
  );
  camera.fov = guiParams.camera.fov;
  camera.updateProjectionMatrix();

  // 使用GUI中的lookAt参数
  camera.lookAt(
    guiParams.camera.lookAtX,
    guiParams.camera.lookAtY,
    guiParams.camera.lookAtZ
  );

  console.log("相机位置:", camera.position);
  console.log(
    "相机看向:",
    guiParams.model.x,
    guiParams.model.y,
    guiParams.model.z
  );
  console.log("场景对象数量:", scene.children.length);

  // 初始化GUI
  initGUI();

  // 开始动画
  animate();
}

// 鼠标点击事件处理
function onMouseClick(event: MouseEvent) {
  // 计算鼠标位置（标准化设备坐标）
  const rect = renderer.domElement.getBoundingClientRect();
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

  // 更新射线投射器
  raycaster.setFromCamera(mouse, camera);

  // 正常的池子选择逻辑
  const intersects = raycaster.intersectObjects(poolObjects, true);

  if (intersects.length > 0) {
    const clickedObject = intersects[0].object;

    // 找到包含"养鱼池"名称的父对象
    let poolObject = clickedObject;
    while (poolObject && !poolObject.name.includes("养鱼池")) {
      poolObject = poolObject.parent as THREE.Object3D;
    }

    if (poolObject && poolObject.name.includes("养鱼池")) {
      // 手动点击时暂停轮播
      restartAutoRotation();
      // 传递点击位置信息
      selectPool(poolObject, intersects[0].point);
    }
  }
}

// 选择并高亮养鱼池
function selectPool(pool: THREE.Object3D, clickPoint?: THREE.Vector3) {
  // 取消之前的选择
  if (selectedPool) {
    resetPoolHighlight(selectedPool);
  }

  // 设置新的选择
  selectedPool = pool;
  highlightPool(pool);

  // 显示弹窗
  showPoolPopup(pool, clickPoint);

  console.log(`选中养鱼池: ${pool.name}`, {
    position: pool.position,
    userData: pool.userData,
  });
}

// 显示养鱼池信息弹窗
function showPoolPopup(pool: THREE.Object3D, clickPoint?: THREE.Vector3) {
  // 从池子对象中获取编号
  const poolNumber = getPoolNumber(pool);
  const poolData = getPoolData(poolNumber);

  selectedPoolInfo.value = {
    name: poolData.name,
    ph: poolData.ph,
    temperature: poolData.temperature,
    oxygen: poolData.oxygen,
    waterLevel: poolData.waterLevel,
    salinity: poolData.salinity,
    isAlarm: poolData.isAlarm,
  };

  // 计算弹窗位置（在池子上方）
  if (container.value) {
    let screenPosition;

    if (clickPoint) {
      // 使用点击点的位置
      screenPosition = worldToScreen(clickPoint);
    } else {
      // 使用池子中心位置
      const poolWorldPosition = new THREE.Vector3();
      pool.getWorldPosition(poolWorldPosition);
      screenPosition = worldToScreen(poolWorldPosition);
    }

    // 计算弹窗位置和箭头位置
    const popupWidth = 180;
    const popupHeight = 140;
    const arrowSize = 8;
    const margin = 20; // 弹窗与池子之间的间距

    // 池子在屏幕上的位置（箭头要指向这里）
    // const poolScreenX = screenPosition.x;
    const poolScreenY = screenPosition.y;

    // 弹窗默认放在池子上方，居中对齐
    // let popupX = poolScreenX - popupWidth / 2;
    let popupY = poolScreenY - popupHeight - arrowSize - margin;
    let isBelow = false;

    // 只在上方空间不够时才放到下方
    if (popupY < 10) {
      popupY = poolScreenY + arrowSize + margin;
      isBelow = true;
    }

    // 箭头始终指向池子中心，相对于弹窗的位置
    const arrowLeft = popupWidth / 2; // 箭头在弹窗中央

    popupPosition.value = {
      x: 0,
      y: 0,
      arrowLeft: arrowLeft,
      isBelow: isBelow,
    };
  }

  showPoolInfo.value = true;
}

// 将3D世界坐标转换为屏幕坐标
function worldToScreen(worldPosition: THREE.Vector3) {
  const vector = worldPosition.clone();
  vector.project(camera);

  const rect = container.value?.getBoundingClientRect();
  if (!rect) return { x: 0, y: 0 };

  const x = (vector.x * 0.5 + 0.5) * rect.width;
  const y = (vector.y * -0.5 + 0.5) * rect.height;

  return { x, y };
}

// 关闭弹窗
function closePopup() {
  showPoolInfo.value = false;

  // 取消高亮
  if (selectedPool) {
    resetPoolHighlight(selectedPool);
    selectedPool = null;
  }
}

// 开始自动轮播
function startAutoRotation() {
  if (autoRotationTimer || poolObjects.length === 0) return;

  isAutoRotating.value = true;
  guiParams.rotation.enabled = true;
  currentRotationIndex = 0;

  autoRotationTimer = setInterval(() => {
    if (poolObjects.length > 0) {
      const pool = poolObjects[currentRotationIndex];
      selectPool(pool);

      currentRotationIndex = (currentRotationIndex + 1) % poolObjects.length;
    }
  }, guiParams.rotation.speed); // 使用GUI参数中的速度
}

// 停止自动轮播
function stopAutoRotation() {
  if (autoRotationTimer) {
    clearInterval(autoRotationTimer);
    autoRotationTimer = null;
    isAutoRotating.value = false;
    guiParams.rotation.enabled = false;
  }
}

// 重启自动轮播（用于手动点击后的恢复）
function restartAutoRotation() {
  stopAutoRotation();
  setTimeout(() => {
    startAutoRotation();
  }, 5000); // 5秒后重新开始轮播
}

// 高亮养鱼池
function highlightPool(pool: THREE.Object3D) {
  const poolNumber = getPoolNumber(pool);
  const poolData = getPoolData(poolNumber);
  const isAlarm = poolData.isAlarm;

  pool.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      // 保存原始材质
      if (!originalMaterials.has(child)) {
        originalMaterials.set(child, child.material);
      }

      // 根据报警状态创建不同颜色的高亮材质
      const highlightMaterial = new THREE.MeshBasicMaterial({
        color: isAlarm ? 0xff0000 : 0x00ff00, // 报警时红色，正常时绿色
        transparent: true,
        opacity: 0.7,
      });

      child.material = highlightMaterial;
    }
  });
}

// 重置养鱼池高亮
function resetPoolHighlight(pool: THREE.Object3D) {
  pool.traverse((child) => {
    if (child instanceof THREE.Mesh && originalMaterials.has(child)) {
      // 直接恢复原始材质
      const originalMaterial = originalMaterials.get(child) as
        | THREE.Material
        | THREE.Material[];
      child.material = originalMaterial;
    }
  });
}

// 调试模型对象名称
function debugModelObjects(object: THREE.Object3D) {
  console.log("=== 模型对象结构调试 ===");

  function traverse(obj: THREE.Object3D, depth = 0) {
    const indent = "  ".repeat(depth);
    const objType = obj.constructor.name;
    const objName = obj.name || "未命名";

    console.log(`${indent}${objType}: "${objName}"`);

    if (obj instanceof THREE.Mesh) {
      console.log(
        `${indent}  - 网格材质类型: ${obj.material.constructor.name}`
      );
      if (Array.isArray(obj.material)) {
        obj.material.forEach((mat, index) => {
          console.log(`${indent}    [${index}] ${mat.constructor.name}`);
          if (mat.transparent) {
            console.log(`${indent}      透明: true, 透明度: ${mat.opacity}`);
          }
        });
      } else {
        if (obj.material.transparent) {
          console.log(
            `${indent}    透明: true, 透明度: ${obj.material.opacity}`
          );
        }
        // 检查材质颜色
        if (obj.material.color) {
          console.log(
            `${indent}    颜色: #${obj.material.color.getHexString()}`
          );
        }
      }
    }

    // 递归遍历子对象
    obj.children.forEach((child) => traverse(child, depth + 1));
  }

  traverse(object);
  console.log("=== 调试完成 ===");
}

// 遍历场景中的所有对象，查找养鱼池
function findAndPrintPools(object: THREE.Object3D) {
  const pools: Array<{ name: string; position: THREE.Vector3; userData: any }> =
    [];

  let poolCounter = 1; // 池子计数器

  // 递归遍历所有子对象
  function traverse(obj: THREE.Object3D) {
    // 检查对象名称是否包含"养鱼池"
    if (obj.name && obj.name.includes("养鱼池")) {
      pools.push({
        name: obj.name,
        position: obj.position.clone(),
        userData: obj.userData,
      });

      // 为这个池子对象添加一个唯一的编号
      obj.userData.poolNumber = poolCounter;
      poolCounter++;

      // 将养鱼池对象添加到可点击对象数组中
      poolObjects.push(obj);
    }

    // 递归遍历子对象
    obj.children.forEach((child) => traverse(child));
  }

  traverse(object);

  console.log(`总共找到 ${pools.length} 个养鱼池`);

  return pools;
}

// 处理水面材质
function processWaterMaterials(object: THREE.Object3D) {
  console.log("开始处理水面材质...");

  object.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      const meshName = child.name.toLowerCase();

      // 检查是否是水面相关的网格
      if (
        meshName.includes("water") ||
        meshName.includes("水") ||
        meshName.includes("surface") ||
        meshName.includes("液体")
      ) {
        // 保存原始材质
        if (!originalMaterials.has(child)) {
          originalMaterials.set(child, child.material);
        }

        // 创建水面材质
        const waterMaterial = new THREE.MeshPhysicalMaterial({
          color: 0x87ceeb, // 更亮的天蓝色
          transparent: true,
          opacity: 0.7,
          roughness: 0.05, // 更光滑的表面
          metalness: 0.1,
          clearcoat: 1.0,
          clearcoatRoughness: 0.05,
          transmission: 0.6, // 减少透射，增加反射
          thickness: 0.3,
          ior: 1.33, // 水的折射率
          reflectivity: 0.8, // 增加反射率
          side: THREE.DoubleSide,
          envMapIntensity: 2.0, // 增加环境贴图强度
        });

        child.material = waterMaterial;
        child.renderOrder = 1; // 确保水面在其他物体之后渲染
      }
      // 检查是否有透明材质需要特殊处理
      else if (child.material) {
        if (Array.isArray(child.material)) {
          child.material.forEach((mat, index) => {
            if (mat.transparent && mat.opacity < 1) {
              console.log(
                `发现透明材质: ${child.name}[${index}], opacity: ${mat.opacity}`
              );
              // 确保透明材质正确渲染
              mat.depthWrite = false;
              mat.side = THREE.DoubleSide;
            }
          });
        } else {
          if (child.material.transparent && child.material.opacity < 1) {
            console.log(
              `发现透明材质: ${child.name}, opacity: ${child.material.opacity}`
            );
            // 确保透明材质正确渲染
            child.material.depthWrite = false;
            child.material.side = THREE.DoubleSide;
          }
        }
      }
    }
  });

  console.log("水面材质处理完成");
}

// 更新水面材质参数
function updateWaterMaterials(params: any) {
  if (!poolModel) return;

  poolModel.traverse((child) => {
    if (
      child instanceof THREE.Mesh &&
      child.material instanceof THREE.MeshPhysicalMaterial
    ) {
      // 检查是否是水面材质（通过材质属性判断）
      if (child.material.transmission > 0.3) {
        child.material.opacity = params.opacity;
        child.material.color.setHex(parseInt(params.color.replace("#", "0x")));
        child.material.roughness = params.roughness;
        child.material.transmission = params.transmission;
        child.material.ior = params.ior;
        if (params.reflectivity !== undefined) {
          child.material.reflectivity = params.reflectivity;
        }
        child.material.needsUpdate = true;
        console.log(`更新水面材质: ${child.name}`);
      }
    }
  });
}

// 强制为所有透明对象应用水面材质
function forceApplyWaterMaterial(object: THREE.Object3D) {
  console.log("强制应用水面材质到所有透明对象...");

  object.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      let shouldApplyWater = false;

      // 检查是否有透明材质
      if (Array.isArray(child.material)) {
        shouldApplyWater = child.material.some(
          (mat) => mat.transparent && mat.opacity < 1
        );
      } else {
        shouldApplyWater =
          child.material.transparent && child.material.opacity < 1;
      }

      if (shouldApplyWater) {
        console.log(`强制为透明对象应用水面材质: ${child.name}`);

        // 保存原始材质
        if (!originalMaterials.has(child)) {
          originalMaterials.set(child, child.material);
        }

        // 创建水面材质
        const waterMaterial = new THREE.MeshPhysicalMaterial({
          color: 0x87ceeb, // 更亮的天蓝色
          transparent: true,
          opacity: 0.7,
          roughness: 0.05, // 更光滑的表面
          metalness: 0.1,
          clearcoat: 1.0,
          clearcoatRoughness: 0.05,
          transmission: 0.6, // 减少透射，增加反射
          thickness: 0.3,
          ior: 1.33,
          reflectivity: 0.8, // 增加反射率
          side: THREE.DoubleSide,
          envMapIntensity: 2.0, // 增加环境贴图强度
        });

        child.material = waterMaterial;
        child.renderOrder = 1;
      }
    }
  });

  console.log("强制水面材质应用完成");
}

// 处理混凝土柱子材质
function processConcreteColumnMaterials(object: THREE.Object3D) {
  console.log("开始处理混凝土柱子材质...");

  object.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      const meshName = child.name;

      // 检查是否是混凝土柱子
      if (
        meshName.includes("混凝土") &&
        meshName.includes("矩形") &&
        meshName.includes("柱")
      ) {
        console.log(`发现混凝土柱子: ${meshName}`);

        // 保存原始材质
        if (!originalMaterials.has(child)) {
          originalMaterials.set(child, child.material);
        }

        // 为了更自然的效果，使用真实混凝土的颜色范围
        // 混凝土通常呈现灰色到浅褐色的色调
        const naturalConcreteColors = [
          { color: 0xb8b8b8, name: "浅灰" }, // 新混凝土
          { color: 0xa8a8a8, name: "中灰" }, // 标准混凝土
          { color: 0x9a9a9a, name: "深灰" }, // 老化混凝土
          { color: 0xc0c0c0, name: "偏白灰" }, // 高标号混凝土
          { color: 0xb0b0b0, name: "暖灰" }, // 带有暖色调的混凝土
          { color: 0xa5a5a5, name: "工业灰" }, // 工业用混凝土
        ];

        // 随机选择一个自然的混凝土颜色
        const selectedConcrete =
          naturalConcreteColors[
            Math.floor(Math.random() * naturalConcreteColors.length)
          ];

        // 创建自然的混凝土材质
        const concreteMaterial = new THREE.MeshStandardMaterial({
          color: selectedConcrete.color,
          roughness: 0.85, // 混凝土表面较粗糙
          metalness: 0.0, // 完全非金属
          envMapIntensity: 0.3, // 轻微的环境反射
        });

        child.material = concreteMaterial;
        console.log(
          `已为柱子 ${meshName} 应用混凝土材质，类型: ${
            selectedConcrete.name
          }，颜色: #${selectedConcrete.color.toString(16)}`
        );
      }
    }
  });

  console.log("混凝土柱子材质处理完成");
}

// 更新混凝土柱子材质参数
function updateConcreteColumnMaterials(object: THREE.Object3D, params: any) {
  if (!object) return;

  object.traverse((child) => {
    if (child instanceof THREE.Mesh) {
      const meshName = child.name;

      // 检查是否是混凝土柱子
      if (
        meshName.includes("混凝土") &&
        meshName.includes("矩形") &&
        meshName.includes("柱")
      ) {
        // 检查是否已经是我们创建的混凝土材质
        if (child.material instanceof THREE.MeshStandardMaterial) {
          child.material.color.setHex(
            parseInt(params.color.replace("#", "0x"))
          );
          child.material.roughness = params.roughness;
          child.material.metalness = params.metalness;
          child.material.envMapIntensity = params.envMapIntensity;
          child.material.needsUpdate = true;
          console.log(`更新柱子材质: ${meshName}`);
        }
      }
    }
  });
}

// 加载GLB模型
function loadPoolModel() {
  const loader = new GLTFLoader();

  // 使用指定路径
  const modelPath = "/pool3.glb";
  console.log(`Loading model from: ${modelPath}`);

  loader.load(
    modelPath,
    (gltf) => {
      poolModel = gltf.scene;

      // 调整模型大小和位置 - 使用GUI参数
      gltf.scene.scale.setScalar(guiParams.model.scale);
      gltf.scene.position.set(
        guiParams.model.x,
        guiParams.model.y,
        guiParams.model.z
      );
      gltf.scene.rotation.x = guiParams.model.rotationX;
      gltf.scene.rotation.y = guiParams.model.rotationY;
      gltf.scene.rotation.z = guiParams.model.rotationZ;
      console.log("🚀 ~ loadPoolModel ~  gltf.scene:", gltf.scene);

      // 模型加载完成后，使用GUI中的lookAt参数
      if (camera) {
        camera.lookAt(
          guiParams.camera.lookAtX,
          guiParams.camera.lookAtY,
          guiParams.camera.lookAtZ
        );
        console.log(
          "相机看向位置:",
          guiParams.camera.lookAtX,
          guiParams.camera.lookAtY,
          guiParams.camera.lookAtZ
        );
      }

      // 处理水面材质
      processWaterMaterials(gltf.scene);

      // 处理混凝土柱子材质
      // processConcreteColumnMaterials(gltf.scene);

      // 添加到场景
      scene.add(gltf.scene);

      // 遍历场景中的所有对象，查找养鱼池
      findAndPrintPools(gltf.scene);

      // 模型加载完成后启动自动轮播
      setTimeout(() => {
        startAutoRotation();
      }, 2000); // 延迟2秒启动，确保模型完全加载

      console.log(`Model loaded successfully from: ${modelPath}`);
    },
    (progress) => {
      console.log(
        `Loading progress:`,
        (progress.loaded / progress.total) * 100 + "%"
      );
    },
    (error) => {
      console.error(`Error loading model:`, error);
      // 创建一个简单的替代几何体
      createFallbackGeometry();
    }
  );
}

// 创建备用几何体（如果FBX加载失败）
function createFallbackGeometry() {
  console.log("Creating fallback geometry...");

  // 创建一个简单的池子形状
  const poolGeometry = new THREE.CylinderGeometry(2, 2, 0.5, 32);
  const poolMaterial = new THREE.MeshLambertMaterial({
    color: 0x4a90e2,
    transparent: true,
    opacity: 0.8,
  });

  const pool = new THREE.Mesh(poolGeometry, poolMaterial);
  pool.position.set(0, 0, 0);

  poolModel = new THREE.Group();
  poolModel.add(pool);

  scene.add(poolModel);

  console.log("Fallback pool geometry created");
}

// 渲染函数
function animate() {
  animationId = requestAnimationFrame(animate);

  // 不需要更新控制器，因为没有创建

  // 渲染
  renderer.render(scene, camera);
}

// 处理窗口大小变化
function handleResize() {
  if (!container.value || !camera || !renderer) return;

  const width = container.value.clientWidth;
  const height = container.value.clientHeight;

  camera.aspect = width / height;
  camera.updateProjectionMatrix();
  renderer.setSize(width, height);
}

// 清理资源
function cleanup() {
  // 停止自动轮播
  stopAutoRotation();

  if (animationId) {
    cancelAnimationFrame(animationId);
  }

  // 清理GUI
  if (gui) {
    gui.destroy();
    gui = null;
  }

  // 不需要清理控制器，因为没有创建

  // 清理模型
  if (poolModel) {
    scene.remove(poolModel);
    poolModel = null;
  }

  // 清理事件监听器
  if (renderer && renderer.domElement) {
    renderer.domElement.removeEventListener("click", onMouseClick, false);
  }

  if (renderer) {
    renderer.dispose();
  }

  // 清理数组和映射
  poolObjects.length = 0;
  originalMaterials.clear();
  selectedPool = null;

  window.removeEventListener("resize", handleResize);
}

// 获取报警数据的函数，供其他组件使用
function getAlarmData() {
  const alarms: Array<{
    poolName: string;
    deviceName: string;
    reason: string;
    time: string;
    level: "high" | "medium" | "low";
  }> = [];

  // 遍历前36个池子，检查报警状态
  for (let i = 1; i <= 36; i++) {
    const poolData = getPoolData(i);
    if (poolData.isAlarm) {
      const dataIndex = Math.min(i - 1, 35);
      const data = poolsData[dataIndex];

      if (data) {
        // 检查各项指标，生成具体的报警信息
        if (!isValueNormal("water_temperature", data.water_temperature)) {
          alarms.push({
            poolName: poolData.name,
            deviceName: "温度传感器",
            reason: data.water_temperature > 29 ? "水温过高" : "水温过低",
            time: new Date().toLocaleString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
            }),
            level: "high",
          });
        }

        if (!isValueNormal("PH", data.PH)) {
          alarms.push({
            poolName: poolData.name,
            deviceName: "pH传感器",
            reason: data.PH > 8.8 ? "pH值过高" : "pH值过低",
            time: new Date().toLocaleString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
            }),
            level: "medium",
          });
        }

        if (!isValueNormal("dissolved_oxygen", data.dissolved_oxygen)) {
          alarms.push({
            poolName: poolData.name,
            deviceName: "溶氧仪",
            reason: "溶氧不足",
            time: new Date().toLocaleString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
            }),
            level: "high",
          });
        }

        if (!isValueNormal("water_level", data.water_level)) {
          alarms.push({
            poolName: poolData.name,
            deviceName: "水位传感器",
            reason: data.water_level > 1.45 ? "水位过高" : "水位过低",
            time: new Date().toLocaleString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
            }),
            level: "medium",
          });
        }

        if (!isValueNormal("salinity", data.salinity)) {
          alarms.push({
            poolName: poolData.name,
            deviceName: "盐度计",
            reason: data.salinity > 26 ? "盐度过高" : "盐度过低",
            time: new Date().toLocaleString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
            }),
            level: "low",
          });
        }
      }
    }
  }

  return alarms;
}

// 暴露函数给其他组件使用
defineExpose({
  getAlarmData,
});

// 组件挂载时初始化
onMounted(async () => {
  await loadPoolsData(); // 先加载数据
  initThreeJS();
  window.addEventListener("resize", handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  cleanup();
});
</script>

<style scoped>
.pool-info-popup {
  position: absolute;
  width: 180px;
  background: linear-gradient(
    135deg,
    rgba(0, 20, 40, 0.75) 0%,
    rgba(0, 40, 80, 0.75) 100%
  );
  border: 1px solid rgba(102, 204, 255, 0.4);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2), 0 0 12px rgba(102, 204, 255, 0.15);
  backdrop-filter: blur(8px);
  z-index: 1000;
  animation: popupFadeIn 0.3s ease-out;
  font-family: "Microsoft YaHei", sans-serif;
}

/* 弹窗箭头 */
.popup-arrow {
  position: absolute;
  bottom: -8px;
  left: var(--arrow-left, 50%);
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid rgba(0, 40, 80, 0.75);
  z-index: 1001;
}

.popup-arrow::before {
  content: "";
  position: absolute;
  top: -9px;
  left: -8px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid rgba(102, 204, 255, 0.4);
}

/* 弹窗在池子下方时的箭头样式 */
.popup-below .popup-arrow {
  bottom: auto;
  top: -8px;
  border-top: none;
  border-bottom: 8px solid rgba(0, 40, 80, 0.75);
}

.popup-below .popup-arrow::before {
  top: auto;
  bottom: -9px;
  border-top: none;
  border-bottom: 8px solid rgba(102, 204, 255, 0.4);
}

@keyframes popupFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid rgba(102, 204, 255, 0.2);
  background: linear-gradient(
    90deg,
    rgba(102, 204, 255, 0.08) 0%,
    transparent 100%
  );
}

.popup-title {
  margin: 0;
  color: #66ccff;
  font-size: 12px;
  font-weight: 600;
  text-shadow: 0 0 8px rgba(102, 204, 255, 0.4);
}

.close-btn {
  background: none;
  border: none;
  color: #66ccff;
  font-size: 16px;
  cursor: pointer;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(102, 204, 255, 0.2);
  transform: scale(1.1);
}

.popup-content {
  padding: 10px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.info-item {
  background: rgba(102, 204, 255, 0.03);
  border: 1px solid rgba(102, 204, 255, 0.15);
  border-radius: 4px;
  padding: 6px;
  text-align: center;
  transition: all 0.3s ease;
}

.info-item:hover {
  background: rgba(102, 204, 255, 0.08);
  border-color: rgba(102, 204, 255, 0.3);
  transform: translateY(-1px);
}

.info-label {
  color: rgba(255, 255, 255, 0.6);
  font-size: 10px;
  margin-bottom: 2px;
  font-weight: 500;
}

.info-value {
  color: #66ccff;
  font-size: 12px;
  font-weight: 600;
  text-shadow: 0 0 6px rgba(102, 204, 255, 0.3);
}

/* 特殊的第5个项目占满整行 */
.info-item:nth-child(5) {
  grid-column: 1 / -1;
}

/* 报警状态样式 */
.alarm-popup {
  border-color: rgba(255, 0, 0, 0.5) !important;
  box-shadow: 0 4px 16px rgba(255, 0, 0, 0.2), 0 0 12px rgba(255, 0, 0, 0.25) !important;
  background: linear-gradient(
    135deg,
    rgba(40, 0, 0, 0.75) 0%,
    rgba(80, 0, 0, 0.75) 100%
  ) !important;
}

.alarm-popup .popup-header {
  background: linear-gradient(
    90deg,
    rgba(255, 0, 0, 0.12) 0%,
    transparent 100%
  ) !important;
  border-bottom-color: rgba(255, 0, 0, 0.3) !important;
}

.alarm-popup .popup-title {
  color: #ff6666 !important;
  text-shadow: 0 0 8px rgba(255, 0, 0, 0.4) !important;
}

.alarm-badge {
  background: #ff0000;
  color: white;
  padding: 1px 6px;
  border-radius: 8px;
  font-size: 10px;
  margin-left: 6px;
  animation: alarmBlink 1s infinite;
}

@keyframes alarmBlink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0.5;
  }
}

.alarm-popup .info-item {
  border-color: rgba(255, 0, 0, 0.2) !important;
  background: rgba(255, 0, 0, 0.03) !important;
}

.alarm-popup .info-value {
  color: #ff6666 !important;
  text-shadow: 0 0 6px rgba(255, 0, 0, 0.3) !important;
}

/* 报警状态箭头样式 */
.alarm-arrow {
  border-top-color: rgba(80, 0, 0, 0.75) !important;
}

.alarm-arrow::before {
  border-top-color: rgba(255, 0, 0, 0.5) !important;
}

/* 报警状态弹窗在下方时的箭头样式 */
.popup-below .alarm-arrow {
  border-bottom-color: rgba(80, 0, 0, 0.75) !important;
}

.popup-below .alarm-arrow::before {
  border-bottom-color: rgba(255, 0, 0, 0.5) !important;
}
</style>
